import React, { PureComponent } from 'react';
import Chart from '../../../utils/chart';
import { mapOptions } from './options';

class Map extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      renderer: 'canvas',
      mapOption: null,
      loading: true,
    };
  }

  async componentDidMount() {
    await this.loadMapOptions();
  }

  async componentDidUpdate(prevProps) {
    if (prevProps.mapData !== this.props.mapData || prevProps.selectedCountry !== this.props.selectedCountry) {
      await this.loadMapOptions();
    }
  }

  async loadMapOptions() {
    const { mapData, selectedCountry, globalStatistics } = this.props;
    if (!mapData) {
      this.setState({ mapOption: null, loading: false });
      return;
    }

    try {
      this.setState({ loading: true });
      const option = await mapOptions(mapData, selectedCountry, globalStatistics);
      this.setState({ mapOption: option, loading: false });
    } catch (error) {
      console.error('加载地图配置失败:', error);
      this.setState({ mapOption: null, loading: false });
    }
  }

  handleChartClick = (params) => {
    if (this.props.onCountryClick && params.name) {
      this.props.onCountryClick(params.name);
    }
  };

  render() {
    const { renderer, mapOption, loading } = this.state;
    const { mapData } = this.props;

    if (!mapData) {
      return null;
    }

    if (loading) {
      return (
        <div
          style={{
            width: '10.625rem',
            height: '8.125rem',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#fff',
            fontSize: '14px',
          }}>
          正在加载地图数据...
        </div>
      );
    }

    if (!mapOption) {
      return (
        <div
          style={{
            width: '10.625rem',
            height: '8.125rem',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: '#fff',
            fontSize: '14px',
          }}>
          地图数据加载失败
        </div>
      );
    }

    return (
      <div
        style={{
          width: '10.625rem',
          height: '8.125rem',
        }}>
        <Chart
          renderer={renderer}
          option={mapOption}
          onChartClick={this.handleChartClick}
        />
      </div>
    );
  }
}

export default Map;

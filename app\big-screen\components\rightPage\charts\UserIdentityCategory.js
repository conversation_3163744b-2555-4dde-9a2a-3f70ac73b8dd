import React, { PureComponent } from 'react';
import { CapsuleChart } from '@jiaminghi/data-view-react';

class UserSituation extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      config: {
        // 单位
        unit: '（条）',
        showValue: false,
        data: [],
      },
    };
  }
  render() {
    const { userIdentityCategory } = this.props;
    console.log("UserIdentityCategory data:", userIdentityCategory)

    // 检查是否有有效数据（非空且有值大于0的项）
    const hasValidData = userIdentityCategory &&
      userIdentityCategory.length > 0 &&
      userIdentityCategory.some(item => item.value > 0);

    // 如果没有有效数据，直接显示暂无数据，不使用CapsuleChart
    if (!hasValidData) {
      return (
        <div style={{
          width: '5.85rem',
          height: '2.625rem',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: '#BCDCFF',
          fontSize: '14px'
        }}>
          暂无数据
        </div>
      );
    }

    const config = {
      unit: '（条）',
      showValue: true,
      data: userIdentityCategory,
    };

    console.log("CapsuleChart config:", config);
    return (
      <div>
        <CapsuleChart
          config={config}
          style={{
            width: '5.85rem',
            height: '2.625rem',
          }}
        />
      </div>
    );
  }
}

export default UserSituation;

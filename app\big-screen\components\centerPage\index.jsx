import React, { PureComponent } from 'react';
import { CenterPage, CenterBottom } from './style';
import Map from './charts/Map';

class index extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      selectedCountry: null, // 当前选中的国家
      currentDataType: 'asean', // 'asean' | 'country'
      countrySpecificData: {}, // 各国家的具体数据
      globalRealData: null, // 全局真实数据
      loading: false // 数据加载状态
    };
  }

  // 组件挂载时使用传入的全局数据
  componentDidMount() {
    // 使用从父组件传入的全局仪表板数据
    if (this.props.globalDashboard) {
      this.setState({ globalRealData: this.props.globalDashboard });
    }
  }

  // 当props更新时同步数据
  componentDidUpdate(prevProps) {
    if (prevProps.globalDashboard !== this.props.globalDashboard && this.props.globalDashboard) {
      this.setState({ globalRealData: this.props.globalDashboard });
    }
  }

  // 获取空数据状态
  getEmptyDetailsList() {
    return [
      { title: '文献生成问题', number: 0, unit: '个' },
      { title: '问题生成数据集', number: 0, unit: '个' },
      { title: '问题总数', number: 0, unit: '个' },
      { title: '数据集总数', number: 0, unit: '个' },
      { title: '导入文件总数', number: 0, unit: '个' },
      { title: '导出文件总数', number: 0, unit: '个' },
    ];
  }

  // 使用缓存服务加载全局统计数据（备用方法）
  loadGlobalData = async () => {
    if (!this.props.dataCacheService) {
      console.warn('数据缓存服务不可用');
      return;
    }

    try {
      this.setState({ loading: true });
      const data = await this.props.dataCacheService.fetchDashboardData();
      this.setState({ globalRealData: data });
    } catch (error) {
      console.error('加载全局统计数据出错:', error);
    } finally {
      this.setState({ loading: false });
    }
  };

  // 生成东盟十国展示数据
  generateAseanMapData = () => {
    // 东盟十国（ASEAN）的基本样式配置
    const aseanCountriesData = {
      'Brunei': {
        color: 'rgba(73,86,166,.4)',
        borderColor: 'rgba(255,209,163, .8)',
        emphasisColor: 'rgba(102,105,240,.6)'
      },
      'Cambodia': {
        color: 'rgba(73,86,166,.4)',
        borderColor: 'rgba(255,209,163, .8)',
        emphasisColor: 'rgba(102,105,240,.6)'
      },
      'Indonesia': {
        color: 'rgba(73,86,166,.5)',
        borderColor: 'rgba(255,209,163, .9)',
        emphasisColor: 'rgba(102,105,240,.7)'
      },
      'Laos': {
        color: 'rgba(73,86,166,.4)',
        borderColor: 'rgba(255,209,163, .8)',
        emphasisColor: 'rgba(102,105,240,.6)'
      },
      'Malaysia': {
        color: 'rgba(73,86,166,.5)',
        borderColor: 'rgba(255,209,163, .9)',
        emphasisColor: 'rgba(102,105,240,.7)'
      },
      'Myanmar': {
        color: 'rgba(73,86,166,.4)',
        borderColor: 'rgba(255,209,163, .8)',
        emphasisColor: 'rgba(102,105,240,.6)'
      },
      'Philippines': {
        color: 'rgba(73,86,166,.5)',
        borderColor: 'rgba(255,209,163, .9)',
        emphasisColor: 'rgba(102,105,240,.7)'
      },
      'Singapore': {
        color: 'rgba(73,86,166,.6)',
        borderColor: 'rgba(255,209,163, 1)',
        emphasisColor: 'rgba(102,105,240,.8)'
      },
      'Thailand': {
        color: 'rgba(73,86,166,.5)',
        borderColor: 'rgba(255,209,163, .9)',
        emphasisColor: 'rgba(102,105,240,.7)'
      },
      'Vietnam': {
        color: 'rgba(73,86,166,.5)',
        borderColor: 'rgba(255,209,163, .9)',
        emphasisColor: 'rgba(102,105,240,.7)'
      }
    };

    return {
      moveLines: [],
      title: '东盟十国地图',
      subtitle: '',
      showAseanOnly: true,
      countryData: aseanCountriesData
    };
  };

  // 处理国家点击事件
  handleCountryClick = (countryName) => {
    const { selectedCountry } = this.state;
    const { onCountrySelect } = this.props;

    if (selectedCountry === countryName) {
      // 如果点击的是已选中的国家，则取消选中
      this.setState({
        selectedCountry: null,
        currentDataType: 'asean'
      });
      // 通知父组件
      if (onCountrySelect) {
        onCountrySelect(null);
      }
    } else {
      // 选中新国家
      this.setState({
        selectedCountry: countryName,
        currentDataType: 'country'
      });

      // 加载该国家的数据
      this.loadCountryData(countryName);

      // 通知父组件
      if (onCountrySelect) {
        onCountrySelect(countryName);
      }
    }
  };

  // 使用缓存服务加载国家特定数据
  loadCountryData = async (countryName) => {
    if (!this.props.dataCacheService) {
      console.warn('数据缓存服务不可用');
      return;
    }

    try {
      this.setState({ loading: true });

      // 使用缓存服务获取数据（优先使用缓存）
      const data = await this.props.dataCacheService.fetchDashboardData(countryName);
      const countryData = this.formatCountryData(data);

      this.setState({
        countrySpecificData: {
          ...this.state.countrySpecificData,
          [countryName]: countryData
        }
      });


    } catch (error) {
      console.error('加载国家统计数据出错:', error);
      // 使用空数据作为备用
      const countryData = this.generateCountrySpecificData();
      this.setState({
        countrySpecificData: {
          ...this.state.countrySpecificData,
          [countryName]: countryData
        }
      });
    } finally {
      this.setState({ loading: false });
    }
  };

  // 格式化真实数据为显示格式
  formatCountryData = (data) => {
    return [
      { title: '文献生成问题', number: data.documentToQuestionCount, unit: '个' },
      { title: '问题生成数据集', number: data.questionToDatasetCount, unit: '个' },
      { title: '问题总数', number: data.questionCount, unit: '个' },
      { title: '数据集总数', number: data.datasetCount, unit: '个' },
      { title: '导入文件总数', number: data.importFileCount, unit: '个' },
      { title: '导出文件总数', number: data.exportFileCount, unit: '个' }
    ];
  };

  // 格式化全局真实数据为显示格式
  formatGlobalData = (data) => {
    return [
      { title: '文献生成问题', number: data.documentToQuestionCount, unit: '个' },
      { title: '问题生成数据集', number: data.questionToDatasetCount, unit: '个' },
      { title: '问题总数', number: data.questionCount, unit: '个' },
      { title: '数据集总数', number: data.datasetCount, unit: '个' },
      { title: '导入文件总数', number: data.importFileCount, unit: '个' },
      { title: '导出文件总数', number: data.exportFileCount, unit: '个' }
    ];
  };

  // 生成国家特定数据（返回空数据）
  generateCountrySpecificData = () => {
    // 直接返回空数据状态
    return this.getEmptyDetailsList();
  };

  // 获取国家显示名称（中文）
  getCountryDisplayName = (countryName) => {
    const nameMap = {
      'Brunei': '文莱',
      'Cambodia': '柬埔寨',
      'Indonesia': '印度尼西亚',
      'Laos': '老挝',
      'Malaysia': '马来西亚',
      'Myanmar': '缅甸',
      'Philippines': '菲律宾',
      'Singapore': '新加坡',
      'Thailand': '泰国',
      'Vietnam': '越南'
    };
    return nameMap[countryName] || countryName;
  };

  render() {
    const { selectedCountry, currentDataType, countrySpecificData, globalRealData, loading } = this.state;
    const { globalStatistics } = this.props;
    const enhancedMapData = this.generateAseanMapData();

    // 根据当前状态决定显示的数据
    let currentDetailsList;
    if (currentDataType === 'country' && selectedCountry) {
      // 显示选中国家的数据
      currentDetailsList = countrySpecificData[selectedCountry] || this.getEmptyDetailsList();
    } else {
      // 显示全局数据（优先使用真实数据）
      currentDetailsList = globalRealData ? this.formatGlobalData(globalRealData) : this.getEmptyDetailsList();
    }

    return (
      <CenterPage>
        <Map
          mapData={enhancedMapData}
          selectedCountry={selectedCountry}
          onCountryClick={this.handleCountryClick}
          globalStatistics={globalStatistics}
        />
        <CenterBottom>
          <div className='detail-list'>
            {loading ? (
              <div style={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                height: '100px',
                color: '#fff',
                fontSize: '14px'
              }}>
                正在加载数据...
              </div>
            ) : (
              currentDetailsList
                ? currentDetailsList.map((item, index) => {
                  return (
                    <div className='detail-list-item' key={index}>
                      <img
                        src={`https://niu-dataset.oss-cn-shenzhen.aliyuncs.com/static/images/center-details-data${index + 1
                          }.png`}
                        alt={item.title}
                      />
                      <div className='detail-item-text'>
                        <h3>{item.title}</h3>
                        <span>{item.number}</span>
                        <span className='unit'>{item.unit}</span>
                      </div>
                    </div>
                  );
                })
                : ''
            )}
          </div>
        </CenterBottom>
      </CenterPage>
    );
  }
}

export default index;

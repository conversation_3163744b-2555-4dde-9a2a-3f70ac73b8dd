import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request, { params }) {
  try {
    const { projectId } = params;

    // 验证项目是否存在
    const project = await prisma.projects.findUnique({
      where: { id: projectId }
    });

    if (!project) {
      return NextResponse.json(
        { error: '项目不存在' },
        { status: 404 }
      );
    }

    // 获取项目统计数据
    const [datasetCount, questionCount, chunkCount] = await Promise.all([
      // 数据集数量
      prisma.datasets.count({
        where: { projectId }
      }),
      
      // 问题数量
      prisma.questions.count({
        where: { projectId }
      }),
      
      // 文档块数量
      prisma.chunks.count({
        where: { projectId }
      })
    ]);

    const statistics = {
      projectId,
      datasetCount,
      questionCount,
      chunkCount,
      lastUpdated: new Date().toISOString()
    };

    return NextResponse.json(statistics);
  } catch (error) {
    console.error('获取项目统计数据失败:', error);
    return NextResponse.json(
      { error: '服务器内部错误' },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}
import styled from 'styled-components';

export const IndexPageStyle = styled.div`
  position: relative;
  overflow: auto;
  margin: 0px;
  padding: 10px 0 20px 0; /* 增加底部内边距 */
  background: url('https://niu-dataset.oss-cn-shenzhen.aliyuncs.com/static/images/pageBg.png') center center no-repeat;
  background-size: cover;
  min-height: 100vh;
  height: auto; /* 改为自适应高度 */

  /* 确保在小屏幕上也能正常显示 */
  @media (max-width: 1400px) {
    padding: 5px 0 15px 0;
  }
`;

export const IndexPageContent = styled.div`
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
  min-height: calc(100vh - 120px); /* 确保最小高度 */
  height: auto; /* 改为自适应高度 */

  .center-page {
    flex: 1;
    min-width: 0; /* 允许收缩 */
  }

  /* 响应式布局 */
  @media (max-width: 1200px) {
    flex-direction: column;

    .center-page {
      order: 1;
    }
  }
`;

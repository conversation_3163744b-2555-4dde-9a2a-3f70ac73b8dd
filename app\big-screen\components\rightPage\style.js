import styled from 'styled-components';

export const RightPage = styled.div`
  width: 6.25rem;
  height: auto;
  padding: 0 0.2rem;
`;

export const RightTopBox = styled.div`
  position: relative;
  height: 3rem;
  width: 100%;
  margin-bottom: 0.25rem;
  .right-top {
    &-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 0.1875rem;
    }
    .earth-gif {
      width: 2.75rem;
      height: auto;
      border-radius: 50%;
      overflow: hidden;
    }
  }
`;

export const RightCenterBox = styled.div`
  position: relative;
  min-height: 2.5rem;
  height: auto;
  width: 100%;
  margin-bottom: 0.25rem;
`;

export const RightBottomBox = styled.div`
  position: relative;
  min-height: 6rem;
  height: auto; /* 改为自适应高度 */
  width: 100%;

  .right-bottom-borderBox13 {
    padding: 0.25rem 0.1875rem 0.1875rem;
    height: auto; /* 改为自适应高度 */

    .right-bottom {
      width: 100%;
      height: auto; /* 改为自适应高度 */
      min-height: 5.5rem;
      border-radius: 10px;
      background-color: rgba(19, 25, 47, 0.6);
      padding-bottom: 0.25rem; /* 添加底部内边距 */

      .feedback-box {
        margin-top: 0.1rem;
        display: flex;
        align-items: flex-start; /* 改为顶部对齐 */
        justify-content: space-around;
        flex-wrap: wrap; /* 允许换行 */
        gap: 0.125rem; /* 添加间距 */

        &-item {
          display: flex;
          align-items: center;
          flex-direction: column;
          min-height: 1.75rem;
          height: auto; /* 改为自适应高度 */
          flex: 1;
          min-width: 0; /* 允许收缩 */

          /* 响应式适配 */
          @media (max-width: 1400px) {
            min-width: calc(50% - 0.125rem); /* 小屏幕时改为两列 */
          }

          .dis-text {
            font-weight: bold;
            margin-top: 0.0625rem;
            color: #b2cfee;
            font-size: 0.2rem;
            background: linear-gradient(to bottom, #fff, #6176F4);
            color: transparent;
            -webkit-background-clip: text;
            background-clip: text;
            text-align: center;
            line-height: 1.2;
            word-break: break-word; /* 长文本换行 */
          }
        }
      }

      .offline-portal-box {
        margin-top: 0.125rem;
        height: auto; /* 改为自适应高度 */
      }
    }
  }
`;

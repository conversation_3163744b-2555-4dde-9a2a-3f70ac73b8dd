<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据大屏适配调试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #1a1a2e;
            color: #fff;
        }
        
        .debug-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            padding: 15px;
            border-radius: 8px;
            font-size: 12px;
            z-index: 9999;
            max-width: 300px;
        }
        
        .debug-item {
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
        }
        
        .debug-label {
            color: #ccc;
        }
        
        .debug-value {
            color: #4CAF50;
            font-weight: bold;
        }
        
        .test-container {
            margin-top: 50px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(19, 25, 47, 0.6);
            border-radius: 10px;
        }
        
        .test-title {
            font-size: 18px;
            margin-bottom: 15px;
            color: #4CAF50;
        }
        
        .mock-detail-list {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .mock-detail-item {
            display: flex;
            align-items: center;
            padding: 10px;
            width: calc(32% - 10px);
            background: rgba(19, 25, 47, 0.8);
            border: 1px solid #343f4b;
            border-radius: 5px;
            min-height: 50px;
        }
        
        .mock-icon {
            width: 30px;
            height: 30px;
            background: #4CAF50;
            border-radius: 50%;
            margin-right: 10px;
            flex-shrink: 0;
        }
        
        .mock-text {
            flex: 1;
        }
        
        .mock-title {
            color: #bcdcff;
            font-size: 14px;
            margin-bottom: 5px;
        }
        
        .mock-value {
            color: #fff;
            font-weight: bold;
        }
        
        @media (max-width: 1400px) {
            .mock-detail-item {
                width: calc(48% - 10px);
            }
        }
        
        @media (max-width: 1000px) {
            .mock-detail-item {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="debug-panel" id="debugPanel">
        <div class="debug-item">
            <span class="debug-label">屏幕宽度:</span>
            <span class="debug-value" id="screenWidth">-</span>
        </div>
        <div class="debug-item">
            <span class="debug-label">屏幕高度:</span>
            <span class="debug-value" id="screenHeight">-</span>
        </div>
        <div class="debug-item">
            <span class="debug-label">DPR:</span>
            <span class="debug-value" id="dpr">-</span>
        </div>
        <div class="debug-item">
            <span class="debug-label">根字体大小:</span>
            <span class="debug-value" id="fontSize">-</span>
        </div>
        <div class="debug-item">
            <span class="debug-label">屏幕类型:</span>
            <span class="debug-value" id="screenType">-</span>
        </div>
        <div class="debug-item">
            <span class="debug-label">物理分辨率:</span>
            <span class="debug-value" id="physicalRes">-</span>
        </div>
    </div>

    <div class="test-container">
        <div class="test-section">
            <div class="test-title">屏幕信息检测</div>
            <p>当前页面用于测试数据大屏在不同屏幕上的适配效果。</p>
            <p>请在笔记本屏幕和外接显示器上分别打开此页面，观察右上角的调试信息。</p>
        </div>

        <div class="test-section">
            <div class="test-title">模拟底部数据展示区域</div>
            <div class="mock-detail-list">
                <div class="mock-detail-item">
                    <div class="mock-icon"></div>
                    <div class="mock-text">
                        <div class="mock-title">文档生成数量</div>
                        <div class="mock-value">1,234 个</div>
                    </div>
                </div>
                <div class="mock-detail-item">
                    <div class="mock-icon"></div>
                    <div class="mock-text">
                        <div class="mock-title">数据处理量</div>
                        <div class="mock-value">5.67 GB</div>
                    </div>
                </div>
                <div class="mock-detail-item">
                    <div class="mock-icon"></div>
                    <div class="mock-text">
                        <div class="mock-title">活跃用户数</div>
                        <div class="mock-value">89 人</div>
                    </div>
                </div>
                <div class="mock-detail-item">
                    <div class="mock-icon"></div>
                    <div class="mock-text">
                        <div class="mock-title">系统响应时间</div>
                        <div class="mock-value">120 ms</div>
                    </div>
                </div>
                <div class="mock-detail-item">
                    <div class="mock-icon"></div>
                    <div class="mock-text">
                        <div class="mock-title">成功率</div>
                        <div class="mock-value">99.8%</div>
                    </div>
                </div>
                <div class="mock-detail-item">
                    <div class="mock-icon"></div>
                    <div class="mock-text">
                        <div class="mock-title">错误数量</div>
                        <div class="mock-value">2 个</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function updateDebugInfo() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            const dpr = window.devicePixelRatio || 1;
            const fontSize = parseFloat(getComputedStyle(document.documentElement).fontSize);
            const physicalWidth = width * dpr;
            const physicalHeight = height * dpr;
            
            // 判断屏幕类型
            const isLaptop = (dpr > 1.1 && width < 1600) || (width <= 1366 && dpr >= 1.25);
            const isExternalMonitor = dpr === 1.0 && width >= 1920;
            let screenType = 'default';
            if (isLaptop) screenType = 'laptop';
            else if (isExternalMonitor) screenType = 'external';

            document.getElementById('screenWidth').textContent = width + 'px';
            document.getElementById('screenHeight').textContent = height + 'px';
            document.getElementById('dpr').textContent = dpr;
            document.getElementById('fontSize').textContent = fontSize + 'px';
            document.getElementById('screenType').textContent = screenType;
            document.getElementById('physicalRes').textContent = physicalWidth + 'x' + physicalHeight;
        }

        // 初始化
        updateDebugInfo();

        // 监听窗口大小变化
        window.addEventListener('resize', updateDebugInfo);

        // 每秒更新一次（用于检测屏幕切换）
        setInterval(updateDebugInfo, 1000);
    </script>
</body>
</html>

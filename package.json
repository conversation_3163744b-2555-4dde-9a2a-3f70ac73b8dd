{"name": "easy-dataset", "version": "1.3.9", "private": true, "author": {"name": "牛码科技", "email": "<EMAIL>", "url": "https://nnnmkj.com/"}, "homepage": "https://github.com/ConardLi/easy-dataset", "scripts": {"db:studio": "prisma studio", "db:push": "prisma db push", "db:template": "node prisma/generate-template.js", "dev": "dotenv -e .env.development -- prisma db push && dotenv -e .env.development -- next dev -p 1717", "build:dev": "dotenv -e .env.development -- prisma db push --accept-data-loss && dotenv -e .env.development -- next build", "start:dev": "dotenv -e .env.development -- next start -p 1717", "build:prod": "dotenv -e .env.production -- prisma db push --accept-data-loss && dotenv -e .env.production -- next build", "start:prod": "dotenv -e .env.production -- next start -p 1717", "lint": "next lint", "electron": "electron .", "electron-dev": "concurrently \"pnpm dev\" \"wait-on http://localhost:1717 && electron .\"", "electron-pack": "electron-builder --dir", "electron-dist": "electron-builder", "clean-dist": "rm -rf dist", "electron-build": "pnpm clean-dist && pnpm db:template && prisma db push && next build && electron-builder -mwl", "electron-build-mac": "pnpm clean-dist && pnpm db:template && prisma db push && next build && electron-builder --mac", "electron-build-win": "pnpm clean-dist && pnpm db:template && prisma db push && next build && electron-builder --win", "electron-build-linux": "pnpm clean-dist && pnpm db:template && prisma db push && next build && electron-builder --linux", "docker": "docker build -t easy-dataset .", "prettier": "npx prettier --write .", "migrate:images": "node scripts/migrate-images-to-oss.js"}, "bin": "desktop/server.js", "pkg": {"assets": [".next/**/*", "public/**/*", "locales/**/*", "package.json", "node_modules/next/**/*"], "targets": ["node18-macos-arm64", "node18-macos-x64", "node18-win-x64", "node18-linux-x64"], "outputPath": "dist"}, "dependencies": {"@ai-sdk/openai": "^1.3.9", "@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@fontsource/inter": "^5.0.16", "@fontsource/jetbrains-mono": "^5.0.18", "@huggingface/hub": "^2.0.2", "@jiaminghi/data-view-react": "^1.2.5", "@langchain/core": "^0.3.66", "@langchain/textsplitters": "^0.1.0", "@lobehub/icons": "^1.96.0", "@mui/icons-material": "5.16.14", "@mui/lab": "5.0.0-alpha.175", "@mui/material": "5.16.14", "@opendocsg/pdf2md": "^0.2.1", "@openrouter/ai-sdk-provider": "^0.4.5", "@prisma/client": "^6.6.0", "adm-zip": "^0.5.16", "ai": "^4.3.4", "ali-oss": "^6.21.0", "antd": "^5.26.7", "axios": "^1.8.4", "dotenv": "^17.2.0", "dotenv-cli": "^8.0.0", "dva": "^2.4.1", "echarts": "^5.5.1", "electron-updater": "^6.3.9", "formidable": "^3.5.2", "framer-motion": "^12.4.10", "i18next": "^24.2.2", "i18next-browser-languagedetector": "^8.0.4", "jotai": "^2.12.3", "langchain": "^0.3.24", "mammoth": "^1.9.0", "mysql2": "^3.14.2", "nanoid": "^5.1.5", "next": "^14.2.29", "next-themes": "^0.2.1", "ollama-ai-provider": "^1.2.0", "opener": "^1.5.2", "pdf2md-js": "^1.0.8", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.4.1", "react-markdown": "^10.0.1", "react-router": "^6.26.2", "react-router-dom": "^6.26.2", "sharp": "^0.33.1", "sonner": "^2.0.3", "styled-components": "^6.1.13", "turndown": "^7.2.0", "xlsx": "^0.18.5", "zhipu-ai-provider": "^0.1.1", "zrender": "^5.4.0"}, "license": "AGPL 3.0", "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "concurrently": "^8.2.2", "electron": "^35.0.0", "electron-builder": "^24.13.3", "husky": "^9.1.7", "lint-staged": "15.5.2", "pkg": "^5.8.1", "prisma": "^6.6.0", "wait-on": "^7.2.0"}, "lint-staged": {"*.{js,jsx,ts,tsx,json,md}": "npm run prettier"}, "main": "electron/main.js", "description": "一个用于创建大模型微调数据集的应用程序", "build": {"appId": "com.easydataset.app", "productName": "Easy Dataset", "files": [".next/**/*", "!.next/cache/**/*", "public/**/*", "locales/**/*", "package.json", "electron/**/*", "node_modules/**/*", "!node_modules/.cache/**/*", "!node_modules/.bin/**/*", "!node_modules/.vite/**/*", "!**/*.{md,d.ts,map}", "!**/node_modules/*/{CHANGELOG.md,README.md,README,readme.md,readme}"], "extraResources": ["prisma/schema.prisma", "prisma/template.sqlite", "prisma/sql.json", "node_modules/.prisma/**/*", "node_modules/@prisma/client/**/*"], "directories": {"buildResources": "public", "output": "dist"}, "asar": true, "asarUnpack": ["**/node_modules/sharp/**/*", "**/node_modules/@img/**/*"], "compression": "maximum", "mac": {"icon": "public/imgs/logo.icns", "category": "public.app-category.developer-tools", "target": [{"target": "dmg", "arch": ["arm64", "x64"]}], "electronLanguages": ["zh_CN", "en"]}, "win": {"icon": "public/imgs/logo.ico", "target": [{"target": "nsis", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "perMachine": false}}}